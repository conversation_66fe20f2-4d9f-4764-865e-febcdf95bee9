import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../../lib/supabase';
import dbService from '../../services/dbService';
import '../styles/Invest.css';

const Invest = ({ user }) => {
  const navigate = useNavigate();
  const [investmentPlans, setInvestmentPlans] = useState([]);
  const [userBalance, setUserBalance] = useState(0);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [investmentAmount, setInvestmentAmount] = useState('');
  const [isInvesting, setIsInvesting] = useState(false);
  const [showInvestModal, setShowInvestModal] = useState(false);
  const [loading, setLoading] = useState(true);
  const [alertModal, setAlertModal] = useState(null);

  useEffect(() => {
    loadInvestmentData();
  }, [user]);

  const loadInvestmentData = async () => {
    try {
      setLoading(true);

      // Load investment plans
      const { data: plans, error: plansError } = await supabase
        .from('investment_plans')
        .select('*')
        .order('min_amount', { ascending: true });

      if (plansError) throw plansError;
      setInvestmentPlans(plans || []);

      // Load user balance
      if (user?.auth_id) {
        const userData = await dbService.getUser(user.auth_id);
        setUserBalance(parseFloat(userData?.balance || 0));
      }
    } catch (error) {
      console.error('Error loading investment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInvestClick = (plan) => {
    setSelectedPlan(plan);
    setInvestmentAmount(parseFloat(plan.min_amount).toString());
    setShowInvestModal(true);
  };

  const handleInvestment = async () => {
    if (!selectedPlan || !investmentAmount || !user?.auth_id) return;

    const amount = parseFloat(investmentAmount);
    const minAmount = parseFloat(selectedPlan.min_amount);
    const maxAmount = selectedPlan.max_amount ? parseFloat(selectedPlan.max_amount) : Infinity;

    // Validation
    if (amount < minAmount) {
      setAlertModal({
        title: 'Invalid Amount',
        message: `Minimum investment for ${selectedPlan.name} plan is $${minAmount.toLocaleString()}`,
        type: 'warning'
      });
      return;
    }

    if (amount > maxAmount) {
      setAlertModal({
        title: 'Invalid Amount',
        message: `Maximum investment for ${selectedPlan.name} plan is $${maxAmount === Infinity ? 'unlimited' : maxAmount.toLocaleString()}`,
        type: 'warning'
      });
      return;
    }

    if (amount > userBalance) {
      setAlertModal({
        title: 'Insufficient Balance',
        message: `You have $${userBalance.toLocaleString()} available. Please deposit more funds to invest $${amount.toLocaleString()}.`,
        type: 'warning'
      });
      return;
    }

    try {
      setIsInvesting(true);

      // Create investment record
      const { data: investment, error: investError } = await supabase
        .from('investments')
        .insert({
          user_id: user.auth_id,
          plan_id: selectedPlan.id,
          amount: amount,
          daily_return: (amount * parseFloat(selectedPlan.daily_return) / 100),
          total_return: 0,
          start_date: new Date().toISOString().split('T')[0],
          end_date: new Date(Date.now() + selectedPlan.duration_days * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: 'active'
        })
        .select()
        .single();

      if (investError) throw investError;

      // Update user balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({ balance: userBalance - amount })
        .eq('auth_id', user.auth_id);

      if (balanceError) throw balanceError;

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert({
          user_id: user.auth_id,
          type: 'investment',
          amount: amount,
          status: 'completed',
          description: `Investment in ${selectedPlan.name} plan`
        });

      if (transactionError) throw transactionError;

      setAlertModal({
        title: 'Investment Successful!',
        message: `Successfully invested $${amount} in ${selectedPlan.name} plan!`,
        type: 'success'
      });
      setShowInvestModal(false);
      setSelectedPlan(null);
      setInvestmentAmount('');

      // Reload data
      await loadInvestmentData();

      // Trigger a user profile refresh to update header balance
      window.dispatchEvent(new CustomEvent('userBalanceUpdated'));

    } catch (error) {
      console.error('Error creating investment:', error);
      setAlertModal({
        title: 'Investment Failed',
        message: 'Failed to create investment. Please try again.',
        type: 'error'
      });
    } finally {
      setIsInvesting(false);
    }
  };

  if (loading) {
    return (
      <div className="invest">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>Loading investment plans...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="invest">
      <div className="invest-header">
        <h1>Investment Plans</h1>
        <p>Choose from our professional investment plans to grow your portfolio</p>
        <div className="balance-display">
          <span className="balance-label">Available Balance:</span>
          <span className="balance-amount">${userBalance.toLocaleString()}</span>
        </div>
      </div>

      <div className="invest-content">
        <div className="investment-plans-grid">
          {investmentPlans.map((plan) => (
            <div key={plan.id} className="investment-plan-card">
              <div className="plan-header">
                <h3 className="plan-name">{plan.name}</h3>
                <div className="plan-returns">
                  <span className="daily-return">{parseFloat(plan.daily_return)}%</span>
                  <span className="return-label">Daily</span>
                </div>
              </div>

              <div className="plan-details">
                <div className="plan-detail">
                  <span className="detail-label">Total Return:</span>
                  <span className="detail-value">{parseFloat(plan.total_return)}%</span>
                </div>
                <div className="plan-detail">
                  <span className="detail-label">Duration:</span>
                  <span className="detail-value">{plan.duration_days} days</span>
                </div>
                <div className="plan-detail">
                  <span className="detail-label">Min Amount:</span>
                  <span className="detail-value">${parseFloat(plan.min_amount).toLocaleString()}</span>
                </div>
                <div className="plan-detail">
                  <span className="detail-label">Max Amount:</span>
                  <span className="detail-value">{plan.max_amount ? `$${parseFloat(plan.max_amount).toLocaleString()}` : 'Unlimited'}</span>
                </div>
              </div>

              <button
                className="invest-btn"
                onClick={() => handleInvestClick(plan)}
                disabled={userBalance < parseFloat(plan.min_amount)}
              >
                {userBalance < parseFloat(plan.min_amount) ? 'Insufficient Balance' : 'Invest Now'}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Investment Modal */}
      {showInvestModal && selectedPlan && (
        <div className="invest-modal-overlay">
          <div className="invest-modal">
            <div className="modal-header">
              <h3>Invest in {selectedPlan.name}</h3>
              <button
                className="close-btn"
                onClick={() => setShowInvestModal(false)}
              >
                ×
              </button>
            </div>

            <div className="modal-content">
              <div className="plan-summary">
                <div className="summary-item">
                  <span>Daily Return:</span>
                  <span>{parseFloat(selectedPlan.daily_return)}%</span>
                </div>
                <div className="summary-item">
                  <span>Total Return:</span>
                  <span>{parseFloat(selectedPlan.total_return)}%</span>
                </div>
                <div className="summary-item">
                  <span>Duration:</span>
                  <span>{selectedPlan.duration_days} days</span>
                </div>
              </div>

              <div className="amount-input-section">
                <label htmlFor="investment-amount">Investment Amount</label>
                <div className="amount-input-container">
                  <span className="currency-symbol">$</span>
                  <input
                    id="investment-amount"
                    type="number"
                    value={investmentAmount}
                    onChange={(e) => setInvestmentAmount(e.target.value)}
                    min={parseFloat(selectedPlan.min_amount)}
                    max={selectedPlan.max_amount ? Math.min(parseFloat(selectedPlan.max_amount), userBalance) : userBalance}
                    step="0.01"
                    placeholder="Enter amount"
                  />
                </div>
                <div className="amount-limits">
                  Min: ${parseFloat(selectedPlan.min_amount).toLocaleString()} |
                  Max: ${selectedPlan.max_amount ? parseFloat(selectedPlan.max_amount).toLocaleString() : 'Unlimited'}
                  {userBalance < parseFloat(selectedPlan.max_amount || Infinity) && (
                    <div className="balance-limit-warning">
                      ⚠️ Your current balance limits you to ${userBalance.toLocaleString()}
                    </div>
                  )}
                </div>
              </div>

              {investmentAmount && (
                <div className="investment-preview">
                  <h4>Investment Preview</h4>
                  <div className="preview-item">
                    <span>Investment Amount:</span>
                    <span>${parseFloat(investmentAmount || 0).toLocaleString()}</span>
                  </div>
                  <div className="preview-item">
                    <span>Daily Earnings:</span>
                    <span>${(parseFloat(investmentAmount || 0) * parseFloat(selectedPlan.daily_return) / 100).toFixed(2)}</span>
                  </div>
                  <div className="preview-item">
                    <span>Total Expected Return:</span>
                    <span>${(parseFloat(investmentAmount || 0) * parseFloat(selectedPlan.total_return) / 100).toFixed(2)}</span>
                  </div>
                  <div className="preview-item total">
                    <span>Final Amount:</span>
                    <span>${(parseFloat(investmentAmount || 0) * (1 + parseFloat(selectedPlan.total_return) / 100)).toFixed(2)}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="modal-actions">
              <button
                className="cancel-btn"
                onClick={() => setShowInvestModal(false)}
              >
                Cancel
              </button>
              <button
                className="confirm-invest-btn"
                onClick={handleInvestment}
                disabled={isInvesting || !investmentAmount || parseFloat(investmentAmount) < parseFloat(selectedPlan.min_amount)}
              >
                {isInvesting ? 'Processing...' : 'Confirm Investment'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Custom Alert Modal */}
      {alertModal && (
        <div className="modal-overlay" onClick={() => setAlertModal(null)}>
          <div className="alert-modal" onClick={(e) => e.stopPropagation()}>
            <div className={`alert-modal-header ${alertModal.type}`}>
              <div className="alert-icon">
                {alertModal.type === 'success' && '✅'}
                {alertModal.type === 'error' && '❌'}
                {alertModal.type === 'warning' && '⚠️'}
              </div>
              <h3>{alertModal.title}</h3>
            </div>
            <div className="alert-modal-body">
              <p>{alertModal.message}</p>
            </div>
            <div className="alert-modal-actions">
              <button
                onClick={() => setAlertModal(null)}
                className="alert-btn primary"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Invest;
