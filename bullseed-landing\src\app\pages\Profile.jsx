import React, { useState, useEffect } from 'react';
import { supabase } from '../../lib/supabase';
import '../styles/Profile.css';

const Profile = ({ user }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [profileStats, setProfileStats] = useState({
    totalInvested: 0,
    referralCount: 0,
    memberSince: null
  });
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: '',
    address: '',
    city: '',
    state: '',
    postalCode: '',
    country: ''
  });
  const [originalData, setOriginalData] = useState({});
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);

  // Load profile data and stats when component mounts or user changes
  useEffect(() => {
    if (user) {
      loadProfileData();
      loadProfileStats();
    }
  }, [user]);

  const loadProfileData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // Get user profile data
      const { data: userData, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_id', user.id)
        .single();

      if (error) {
        console.error('Error loading profile:', error);
        return;
      }

      const data = {
        firstName: userData.first_name || userData.name?.split(' ')[0] || '',
        lastName: userData.last_name || userData.name?.split(' ')[1] || '',
        email: userData.email || '',
        phone: userData.phone || '',
        dateOfBirth: userData.date_of_birth || '',
        nationality: userData.nationality || '',
        address: userData.address || '',
        city: userData.city || '',
        state: userData.state || '',
        postalCode: userData.postal_code || '',
        country: userData.country || ''
      };

      setProfileData(data);
      setOriginalData(data);
    } catch (error) {
      console.error('Error loading profile data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProfileStats = async () => {
    if (!user) return;

    try {
      // Get total invested amount
      const { data: investments, error: investError } = await supabase
        .from('investments')
        .select('amount')
        .eq('user_id', user.id);

      const totalInvested = investments?.reduce((sum, inv) => sum + parseFloat(inv.amount || 0), 0) || 0;

      // Get referral count
      const { data: referrals, error: refError } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_id', user.id);

      const referralCount = referrals?.length || 0;

      // Get member since date
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('created_at')
        .eq('auth_id', user.id)
        .single();

      const memberSince = userData?.created_at ? new Date(userData.created_at) : null;

      setProfileStats({
        totalInvested,
        referralCount,
        memberSince
      });
    } catch (error) {
      console.error('Error loading profile stats:', error);
    }
  };

  // Show loading state if user data is not yet available
  if (!user) {
    return (
      <div className="profile">
        <div className="profile-loading">
          <div className="loading-spinner"></div>
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      setAvatarFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadAvatar = async () => {
    if (!avatarFile || !user) return null;

    try {
      const fileExt = avatarFile.name.split('.').pop();
      const fileName = `${user.id}-${Date.now()}.${fileExt}`;

      // Upload to Supabase storage
      const { data, error } = await supabase.storage
        .from('profile-images')
        .upload(fileName, avatarFile, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Error uploading avatar:', error);
        // If bucket doesn't exist, we'll skip avatar upload for now
        if (error.message?.includes('Bucket not found')) {
          console.warn('Profile images bucket not found. Skipping avatar upload.');
          return null;
        }
        return null;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-images')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      return null;
    }
  };

  const handleSave = async () => {
    if (!user) return;

    setSaving(true);
    try {
      let avatarUrl = user.avatar_url;

      // Upload new avatar if selected
      if (avatarFile) {
        const newAvatarUrl = await uploadAvatar();
        if (newAvatarUrl) {
          avatarUrl = newAvatarUrl;
        }
      }

      // Update user profile in database
      const { error } = await supabase
        .from('users')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          name: `${profileData.firstName} ${profileData.lastName}`.trim(),
          phone: profileData.phone,
          date_of_birth: profileData.dateOfBirth,
          nationality: profileData.nationality,
          address: profileData.address,
          city: profileData.city,
          state: profileData.state,
          postal_code: profileData.postalCode,
          country: profileData.country,
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString()
        })
        .eq('auth_id', user.id);

      if (error) {
        console.error('Error updating profile:', error);
        alert('Failed to update profile. Please try again.');
        return;
      }

      // Update original data and reset editing state
      setOriginalData(profileData);
      setIsEditing(false);
      setAvatarFile(null);
      setAvatarPreview(null);

      // Trigger a refresh of user data in parent component
      window.dispatchEvent(new CustomEvent('userProfileUpdated'));

      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Error saving profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to original data
    setProfileData(originalData);
    setIsEditing(false);
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  return (
    <div className="profile">
      <div className="profile-header">
        <h1>Profile Settings</h1>
        <p>Manage your personal information and account details</p>
      </div>

      <div className="profile-content">
        <div className="profile-card">
          <div className="profile-card-header">
            <div className="profile-avatar-section">
              <div className="profile-avatar">
                {avatarPreview || user.avatar_url ? (
                  <img src={avatarPreview || user.avatar_url} alt={user.name || 'User'} />
                ) : (
                  <div className="profile-avatar-placeholder">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                      <circle cx="12" cy="7" r="4"/>
                    </svg>
                  </div>
                )}
              </div>
              <div className="profile-avatar-info">
                <h2>{user.name || 'User'}</h2>
                <p>{user.email || 'No email'}</p>
                <label className="profile-avatar-btn" htmlFor="avatar-upload">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                    <circle cx="12" cy="13" r="4"/>
                  </svg>
                  Change Photo
                </label>
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  style={{ display: 'none' }}
                />
              </div>
            </div>
            
            <div className="profile-actions">
              {!isEditing ? (
                <button className="profile-edit-btn" onClick={() => setIsEditing(true)}>
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                  </svg>
                  Edit Profile
                </button>
              ) : (
                <div className="profile-edit-actions">
                  <button
                    className="profile-save-btn"
                    onClick={handleSave}
                    disabled={saving}
                  >
                    {saving ? (
                      <>
                        <div className="loading-spinner-small"></div>
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                  <button
                    className="profile-cancel-btn"
                    onClick={handleCancel}
                    disabled={saving}
                  >
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="profile-form">
            <div className="profile-section">
              <h3>Personal Information</h3>
              <div className="profile-form-grid">
                <div className="profile-form-group">
                  <label>First Name</label>
                  <input
                    type="text"
                    value={profileData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Last Name</label>
                  <input
                    type="text"
                    value={profileData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Email Address</label>
                  <input
                    type="email"
                    value={profileData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Phone Number</label>
                  <input
                    type="tel"
                    value={profileData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Date of Birth</label>
                  <input
                    type="date"
                    value={profileData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Nationality</label>
                  <select
                    value={profileData.nationality}
                    onChange={(e) => handleInputChange('nationality', e.target.value)}
                    disabled={!isEditing}
                  >
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Australia">Australia</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                    <option value="Japan">Japan</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="profile-section">
              <h3>Address Information</h3>
              <div className="profile-form-grid">
                <div className="profile-form-group full-width">
                  <label>Street Address</label>
                  <input
                    type="text"
                    value={profileData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>City</label>
                  <input
                    type="text"
                    value={profileData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>State/Province</label>
                  <input
                    type="text"
                    value={profileData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Postal Code</label>
                  <input
                    type="text"
                    value={profileData.postalCode}
                    onChange={(e) => handleInputChange('postalCode', e.target.value)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="profile-form-group">
                  <label>Country</label>
                  <select
                    value={profileData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    disabled={!isEditing}
                  >
                    <option value="United States">United States</option>
                    <option value="Canada">Canada</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Australia">Australia</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                    <option value="Japan">Japan</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="profile-stats">
          <div className="profile-stat-card">
            <div className="profile-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M12 2v20m8-10H4"/>
              </svg>
            </div>
            <div className="profile-stat-info">
              <h4>Account Balance</h4>
              <p>${(user.balance || 0).toFixed(2)}</p>
            </div>
          </div>

          <div className="profile-stat-card">
            <div className="profile-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <line x1="12" y1="1" x2="12" y2="23"/>
                <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
              </svg>
            </div>
            <div className="profile-stat-info">
              <h4>Total Invested</h4>
              <p>${profileStats.totalInvested.toFixed(2)}</p>
            </div>
          </div>

          <div className="profile-stat-card">
            <div className="profile-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
                <circle cx="8.5" cy="7" r="4"/>
                <path d="M20 8v6"/>
                <path d="M23 11h-6"/>
              </svg>
            </div>
            <div className="profile-stat-info">
              <h4>Referrals</h4>
              <p>{profileStats.referralCount}</p>
            </div>
          </div>

          <div className="profile-stat-card">
            <div className="profile-stat-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"/>
                <polyline points="12,6 12,12 16,14"/>
              </svg>
            </div>
            <div className="profile-stat-info">
              <h4>Member Since</h4>
              <p>
                {profileStats.memberSince
                  ? profileStats.memberSince.toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long'
                    })
                  : 'N/A'
                }
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
